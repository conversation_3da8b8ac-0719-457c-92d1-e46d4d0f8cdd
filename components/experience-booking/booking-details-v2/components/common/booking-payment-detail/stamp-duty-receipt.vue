<template>
  <div class="base-payment-details">
    <!-- 印花税弹窗 -->
    <klk-modal
      title=""
      button-align="equal"
      :open.sync="openModel"
      :show-cancel-button="false"
    >
      <div class="text-align-center__frf">{{ modelContent }}</div>
      <div slot="footer">
        <klk-button
          size="small"
          style="width: 100%; margin-top: 20px"
          @click="closeModel"
        >
          {{ $t('global.confirm') }}
        </klk-button>
      </div>
    </klk-modal>
    <!-- 填写开具资料 -->
    <klk-modal
      v-if="isDesktop"
      v-bind="{...issueReceipt, title: $t('14061')}"
      @on-close="closeIssueReceipt"
    >
      <IssueFrankedReceipt :order-no="orderGuid" @submit="submitReceipt" />
    </klk-modal>

    <!-- 埋点 FrankedReceiptFailed -->
    <div
      v-galileo-click-tracker="{
        spm: '',
        autoTrackSpm: true
      }"
      class="data-spm-module_FrankedReceiptFailed"

      :data-spm-module="'FrankedReceiptFailed' + spm"
      :data-spm-virtual-item="'__virtual' + spm"
    ></div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, State, Prop } from 'nuxt-property-decorator'
import { ExperienceBookingDetails } from '~/types/experience/booking-details'
import { emit as myEmit, on as myOn } from '~/components/experience-booking/booking-details-v2/base/common'
import { openDeeplink, getModalOpt } from '~/components/experience-booking/booking-details-v2/utils'
import IssueFrankedReceipt from '~/components/experience-booking/booking-details-v2/components/common/issue-franked-receipt/index.vue'

@Component({
  components: {
    IssueFrankedReceipt
  }
})
export default class index extends Vue {
  @Prop() bookingNo!: any
  @Prop() orderGuid!: any
  @Prop() actionList!: any[]
  @State klook!: Data.Klook

  beforeMount() {
    // 订阅印花税按钮事件
    myOn('issu_franked_receipt', (btn: ExperienceBookingDetails.ActionIF) => {
      btn && this.clickActionHandler(btn)
    })
  }

  openModel: boolean = false
  modelContent: string = ''

  closeModel() {
    this.openModel = false
    this.$inhouse.track('action', '.data-spm-module_FrankedReceiptFailed')
  }

  get spm() {
    const oid = '?trg=manual&oid=booking_' + this.bookingNo
    return oid
  }

  // 开具印花税弹窗
  issueReceipt = {
    ...getModalOpt({
      width: '960px',
      titleAlign: 'center',
      size: 'normal',
      escPressClosable: false,
      overlayClosable: false
    })
  }

  async closeIssueReceipt() {
    const okLabel = this.$t('global.confirm')
    const confirm = await this.$confirm(this.$t('14774'), this.$t('15971'), {
      okLabel
    })
    if (confirm.result) {
      this.issueReceipt.open = false
    }
  }

  submitReceipt(success: boolean) {
    // 成功之后改变按钮状态
    if (success) {
      this.actionList.forEach((btn: ExperienceBookingDetails.ActionIF) => {
        if (btn.key === 'issue_franked_receipt') {
          btn.key = 'issuing_franked_receipt'
          btn.text = this.$t('13844')
        }
      })
    }
    this.issueReceipt.open = false
  }

  openDeeplink(href: string) {
    openDeeplink(href, this.klook.platform)
  }

  get isDesktop() {
    return this.klook.platform === 'desktop'
  }

  clickActionHandler(btn: ExperienceBookingDetails.ActionIF) {
    switch (btn.key) {
      case 'order_info_view_more': // 合并支付、查看更多弹窗
        myEmit('handleMergeOption2on', this.orderGuid, '')
        break
      case 'issue_franked_receipt': // 开具收据
        if (this.isDesktop) {
          this.issueReceipt.open = true
        } else {
          btn.deep_link && this.openDeeplink(btn.deep_link)
        }
        break
      case 'issuing_franked_receipt': // 印花税开办中 text id 13496
        this.$alert(this.$t('13496'))
        break
      case 'view_franked_receipt_fail': // 开具失败 text id 13782
        // 弹窗
        this.openModel = true
        this.modelContent = this.$t('13782')
        this.$inhouse.track('exposure', '.data-spm-module_FrankedReceiptFailed')
        break
      default:
        // deep_link
        btn.deep_link && this.openDeeplink(btn.deep_link)
    }
  }
}
</script>

<style lang="scss" scoped>
.text-align-center__frf {
  text-align: center;
  line-height: 22px;
}
</style>
