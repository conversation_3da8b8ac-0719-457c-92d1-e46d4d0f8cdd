<template>
  <div class="booking-status-box" :class="[klook.platform]">
    <div class="status-info">
      <div class="box-top">
        <div class="col-left">
          <div class="status-box">
            <TextTips
              :reset-class="'style-status'"
              v-bind="{
                text: bookingStatus.text,
                color: bookingStatus.color
              }"
              :style="{ marginRight: '4px' }"
            />
            <CountdownTime
              v-if="data.payment_deadline"
              :end-time="data.payment_deadline"
            >
              <template slot-scope="slotProps">
                <TextTips
                  :reset-class="'style-status'"
                  v-bind="{
                    text: slotProps.countDownTime,
                    color: bookingStatus.color
                  }"
                />
              </template>
            </CountdownTime>
          </div>
          <div v-if="bookingStatus.tips" class="sub-text">
            <klk-alert v-if="showAlert(bookingStatus.tips_type)" type="info">
              <div class="status-alert">
                <IconInformation
                  theme="outline"
                  size="24"
                  class="status-alert-icon"
                  :fill="$tokensObj['$color-text-primary']"
                />
                <p class="status-alert-text">{{ bookingStatus.tips }}</p>
              </div>
            </klk-alert>
            <p v-else class="status-desc">
              {{ bookingStatus.tips }}
            </p>
          </div>
        </div>
      </div>
      <div class="box-bottom">
        <div v-if="data.booking_amount || paymentDetail.text" class="col-left">
          <span class="col-left__line-box">
            <span v-if="data.booking_amount" class="booking-amount">{{
              data.booking_amount
            }}</span>
            <LoadingButton :loading="paymentDetailsLoading"><a
              v-if="paymentDetail.text"
              v-galileo-click-tracker="{spm: '', autoTrackSpm: true}"
              v-bind="bindIHTrack(paymentDetail.track)"
              class="alink"
              href="javascript:void(0);"
              @click="clickPaymentDetails"
            >
              {{ paymentDetail.text }}
            </a></LoadingButton>
          </span>
        </div>
        <div v-if="statusBtns && statusBtns.length" class="col-right">
          <TextTips
            v-for="(btn, idx) in statusBtns"
            :key="idx"
            :reset-class="'style-btn'"
            v-bind="btn"
            :size="isDesktop ? 'normal' : 'small'"
            @click="clickBtnStatus(btn)"
          >
          </TextTips>
        </div>
      </div>
    </div>

    <BookingPaymentDetails
      :data="paymentDetailObj.data"
      :on-custom-button-click="onCustomButtonClick"
      @close="paymentDetailObj.on.close"
    />
    <klk-modal
      :class="isDesktop && 'download-modal'"
      :modal-class="!isDesktop ? 'common-klk-style empty-style' : ''"
      v-bind="downloadObj"
      v-on="downloadObj.on"
    >
      <PanelApp></PanelApp>
    </klk-modal>
    <BookingRefundDetails :data="refundDetailObj" />
    <MergeModal
      v-if="mergeOptionVisible"
      :merge-option-visible="mergeOptionVisible"
      :merge-option-info="mergeOptionInfo"
      :order-detail="data"
      @hide-merge-option="hideMergeOption"
      @handle-option-click="handleMergeClick($event, data)"
    />
    <!-- 物流信息 start -->
    <template v-if="isDesktop">
      <klk-modal v-bind="logisticsObj" v-on="logisticsObj.on">
        <LogisticsModalDesktop :logistics="logisticsDetail" />
      </klk-modal>
    </template>
    <template v-else>
      <LogisticsModalMobile
        v-if="logisticsDetail"
        :show-layer="showLogistics"
        :logistics="logisticsDetail"
        @toggle="toggleLogistics"
      />
    </template>
    <!-- 物流信息 end -->
    <!-- 印花税、收据 -->
    <StampDutyReceipt :booking-no="calcBookingRefNo" :order-guid="calcOrderGuid" :action-list="calcPaymentActionList" />
    <!-- 申请退款 end -->
    <ManageRefundDesktop
      v-if="isDesktop"
      :visible.sync="manageRefundObj.syncData.visible"
      :activity-name="manageRefundObj.activity_name"
    />
    <ManageRefundMobile
      v-else
      :visible.sync="manageRefundObj.syncData.visible"
      :activity-name="manageRefundObj.activity_name"
    />
    <!-- 申请退款 end -->

    <!-- delay voucher start -->
    <!-- 立即激活 -->
    <klk-modal
      class="issue-modal"
      :title="$t('168159')"
      :open.sync="issueVoucherModal"
      :title-align="isDesktop ? undefined : 'center'"
      :overlay-closable="false"
    >
      <div class="klk-modal-content">
        <div class="item">
          <IconProtect
            class="icon"
            theme="outline"
            size="16"
            :fill="$tokensObj['$colorTextPrimary']"
          />
          <p class="text">{{ $t('168160') }}</p>
        </div>
        <div class="item">
          <IconWarnCircle
            class="icon"
            theme="outline"
            size="16"
            :fill="$tokensObj['$colorTextPrimary']"
          />
          <p class="text">{{ $t('168161') }}</p>
        </div>
      </div>
      <div slot="footer" :class="['footer', klook.platform]">
        <klk-button
          v-galileo-click-tracker="{spm: '', autoTrackSpm: true}"
          type="outlined"
          size="small"

          data-spm-module="DV_IssueConfirm"
          data-spm-virtual-item="__virtual"
          :loading="issueLoading"
          :disabled="issueLoading"
          @click="handleIssueConfirm"
        >
          {{ $t('168162') }}
        </klk-button>
        <klk-button
          v-galileo-click-tracker="{spm: '', autoTrackSpm: true}"
          type="outlined"
          size="small"

          data-spm-module="DV_IssueLater"
          data-spm-virtual-item="__virtual"
          :disabled="issueLoading"
          @click="issueVoucherModal = false"
        >
          {{ $t('168163') }}
        </klk-button>
      </div>
    </klk-modal>

    <!-- 催促出单 -->
    <klk-modal
      button-align="block"
      :open.sync="speedItUpModal"
      :overlay-closable="false"
      :show-cancel-button="false"
      @on-confirm="speedItUpModal = false"
    >
      <div class="klk-modal-content speed-modal">
        <img class="img" src="https://res.klook.com/image/upload/success_ce0kmn.png" alt="" />
        <div class="title">{{ $t('168168') }}</div>
        <div class="text">{{ $t('168169') }}</div>
      </div>
    </klk-modal>
    <!-- delay voucher end -->
  </div>
</template>

<script lang="ts">
import { Vue, Component, Getter, State, Prop, namespace } from 'nuxt-property-decorator'
import { IconInformation, IconWarnCircle, IconProtect } from '@klook/klook-icons'
import CountdownTime from '~/components/experience-booking/booking-details-v2/components/common/countdown-time/index.vue'
import BookingPaymentDetails from '~/components/experience-booking/booking-details-v2/components/common/booking-payment-detail/index.vue'
import MergeModal from '~/components/experience-booking/booking-details-v2/components/common/merge-option-modal/index.vue'
import { getModalOpt } from '~/components/experience-booking/booking-details-v2/utils/index.js'
import BookingRefundDetails from '~/components/experience-booking/booking-details-v2/components/common/booking-refund-detail/index.vue'
import PanelApp from '~/components/layouts/default/desktop/header/panel-app.vue'
import apis from '~/share/data/apis'
import { ExperienceBookingDetails } from '~/types/experience/booking-details'
import LogisticsModalDesktop from '~/components/experience-booking/booking-details-v2/components/common/logistics-modal/desktop/index.vue'
import LogisticsModalMobile from '~/components/experience-booking/booking-details-v2/components/common/logistics-modal/mobile/index.vue'
import { getLangPathStr } from '~/share/utils'
import { BookingStatusPropDataIf } from '~/components/experience-booking/booking-details-v2/type.d'
import StampDutyReceipt from '~/components/experience-booking/booking-details-v2/components/common/booking-payment-detail/stamp-duty-receipt.vue'
import { openDeeplink, openPdfWithFallback } from '~/components/experience-booking/booking-details-v2/utils'
import ManageRefundDesktop from '~/components/experience-booking/booking-details-v2/components/common/booking-policy/desktop/manage-refund/index.vue'
import ManageRefundMobile from '~/components/experience-booking/booking-details-v2/components/common/booking-policy/mobile/manage-refund/index.vue'
import LoadingButton from '~/components/common/loading-button/index.vue'

import {
  state as myState,
  on as myOn,
  emit as myEmit
} from '~/components/experience-booking/booking-details-v2/base/common'
import { handleIHTrack } from '~/components/experience-booking/booking-details-v2/base/inHouseTrack'

const API_DELAY_VOUCHER_ISSUE_NOW = '/v1/experiencesrv/order/detail_service/activate_delay_voucher'
const authModule = namespace('auth')

@Component({
  components: {
    LoadingButton,
    ManageRefundDesktop,
    ManageRefundMobile,
    StampDutyReceipt,
    CountdownTime,
    BookingPaymentDetails,
    PanelApp,
    BookingRefundDetails,
    MergeModal,
    LogisticsModalDesktop,
    LogisticsModalMobile,
    IconInformation,
    IconWarnCircle,
    IconProtect
  }
})
export default class BookingStatus extends Vue {
  @Prop({ type: Object, default: null }) data!: BookingStatusPropDataIf
  @State klook!: Data.Klook
  @Getter isGuestCheckout!: boolean
  @authModule.State user!: any

  paymentDetailsLoading = false
  issueVoucherModal = false
  speedItUpModal = false
  issueLoading = false

  get isDesktop() {
    return this.klook.platform === 'desktop'
  }

  manageRefundObj = {
    activity_name: '',
    syncData: {
      visible: false
    }
  }

  // 物流信息
  showLogistics = false
  logisticsDetail: null | object = null
  logisticsObj = {
    ...getModalOpt({
      width: '600px'
    }),
    on: {
      close: () => {
        this.logisticsObj.open = false
      }
    }
  }

  mergeOptionVisible = false
  mergeOptionInfo: null | any = null
  beforeMount() {
    myOn('showRefundDetail2on', this.showRefundDetail)
    myOn('handleMergeOption2on', this.handleMergeOption)
    myOn('showDownload2on', this.showDownload)
    myOn('update_payment_button_status', this.updatePaymentButtonStatus)
  }

  bindIHTrack(...args: any) {
    return handleIHTrack(...args)
  }

  get paymentDetail() {
    return this.data?.payment_detail || {}
  }

  downloadObj = {
    from: '',
    ...getModalOpt({
      width: 'auto'
    }),
    on: {
      close: () => {
        this.downloadObj.open = false
      }
    }
  }

  refundDetailObj = {
    isCacheFlag: false,
    visible: false,
    booking_ref_no: '',
    refund_detail: {}
  }

  get calcOrderGuid() {
    return this.data?.order_guid || ''
  }

  get calcBookingRefNo() {
    return this.data?.booking_no || ''
  }

  get calcPaymentActionList() {
    return this.paymentDetailObj.cacheData?.payment_details_buttons || []
  }

  paymentDetailObj: Record<string, any> = {
    cacheData: null,
    data: {
      visible: false
    },
    on: {
      close: () => {
        this.paymentDetailObj.data.visible = false
      },
      show: () => {
        this.paymentDetailObj.data.visible = true
      }
    }
  }

  get bookingStatus() {
    return this.data?.booking_status || {}
  }

  handleFixedBtns(arr: ExperienceBookingDetails.ActionIF[]) {
    const fitlerKeys = ['continue_pay', 'to_verify']
    const fixedArr: any = arr.filter((o: ExperienceBookingDetails.ActionIF) =>
      fitlerKeys.includes(o.key)
    )

    if (fixedArr.length) {
      // MWEB添加到底部按钮
      myState.fixedFooter.list = fixedArr
      myState.fixedFooter.on = {
        click: this.clickBtnStatus
      }
      // 过滤楼层按钮
      arr = arr.filter(
        (o: ExperienceBookingDetails.ActionIF) => !fitlerKeys.includes(o.key)
      )
    }
    return arr
  }

  get statusBtns() {
    const arr = this.calcActionList?.map((o: ExperienceBookingDetails.ActionIF) => {
      const obj: any = {}
      if (o.type === 'outlined_gray') {
        obj.type = 'outlined'
        obj['data-custom-class'] = 'like-disabled-style'
      }
      return {
        ...o,
        ...obj
      }
    })
    // arr = this.handleFixedBtns(arr) // 新版不需要fixed按钮样式
    // const arr = [] // ez test code
    // arr.push({
    //   disabled: false,
    //   key: 'remind_ticket', // ez test code
    //   text: '催单',
    //   type: 'outlined',
    //   tips: '',
    //   deep_link: ''
    // })
    return arr
  }

  showDownload(from: string) {
    const { downloadObj } = this
    downloadObj.open = true
    downloadObj.from = from
  }

  async showRefundDetail(options?: { closeAxiosLoading: boolean, setLoading: Function }) {
    const { closeAxiosLoading = false, setLoading = (_: any) => {} } = options || {}
    if (this.refundDetailObj.isCacheFlag) {
      this.refundDetailObj.visible = true
      return
    }
    const booking_reference_no = this.calcBookingRefNo
    if (!booking_reference_no) {
      return
    }
    const zone = -new Date().getTimezoneOffset() / 60
    typeof setLoading === 'function' && setLoading(true)
    const { success, result } = await this.$axios.$get(
      apis.getBookingRefundDetailV3,
      {
        loading: !closeAxiosLoading,
        params: {
          booking_reference_no,
          zone
        }
      }
    )
    typeof setLoading === 'function' && setLoading(false)
    if (success && result) {
      const { refundDetailObj } = this
      refundDetailObj.visible = true
      refundDetailObj.booking_ref_no = this.calcBookingRefNo
      refundDetailObj.refund_detail = result
      refundDetailObj.isCacheFlag = true
    }
  }

  get calcActionList() {
    return this.data?.actions || []
  }

  async clickRemindTicket() {
    const data = {
      booking_ref_no: this.calcBookingRefNo
    }
    const result = await this.$axios.$post(apis.postBookingTicketChase, data, { loading: true }) as any
    if (result?.success) {
      this.$toast(this.$t('101178'))
      // 催单 -> 已催单
      const arr = this.calcActionList
      const remind_ticket = arr.find(
        (o: ExperienceBookingDetails.ActionIF) => o.key === 'remind_ticket'
      )
      if (remind_ticket) {
        this.$set(remind_ticket, 'disabled', true)
      }
      setTimeout(() => {
        window.location.reload()
      }, 1200)
    } else {
      this.$alert(result?.error?.message ?? 'Error')
    }
  }

  // orderDetail用于构造请求，type用于请求pay/verify,viewType：0 = 查看模式没有按钮 1= 支付模式 2= 身份验证
  handleMergeOption(order_guid: string, type: string) {
    this.$axios
      .get(`/v1/usrcsrv/booking/combo?order_no=${order_guid}&type=${type}`, {
        loading: true
      })
      .then(({ data: resp }) => {
        if (resp.success && resp.result) {
          resp.result.type = type
          this.mergeOptionInfo = resp.result
          this.mergeOptionVisible = true
        } else {
          this.$alert(resp.error && resp.error.message)
        }
      })
  }

  checkOrderCancel(order: any) {
    if (order.has_multiple_booking) {
      this.handleMergeOption(order.order_guid, 'cancel')
    } else {
      this.cancelOrder(order.order_guid)
    }
  }

  cancelOrder(guid: string) {
    const self = this
    const bodyTxt = self.$t('15375')
    this.$confirm(bodyTxt, {
      okLabel: self.$t('14052'),
      cancelLabel: self.$t('13194')
    }).then(({ result }) => {
      if (result) {
        self.$axios
          .post(apis.orderUserCancel, { order_guid: guid }, { checkLogin: true })
          .then(({ data: resp }) => {
            if (resp.success) {
              self.$router.go(0)
            } else {
              resp?.error?.message && self.$alert(resp.error.message)
            }
          })
      }
    })
  }

  checkOrderContinue(order: any, btn: any) {
    if (order.has_multiple_booking) {
      this.handleMergeOption(order.order_guid, 'pay')
    } else {
      this.handleCheckContinue(order, btn?.deep_link)
    }
  }

  handleCheckContinue(order: any, deeplink: string) {
    if (this.klook.platformMp) {
      this.$miniApp.pay({
        orderGuid: order.order_guid, // 订单ID,
        bookingNo: order.booking_no,
        cancelUrl: window.location.href, // TODO 取消支付的跳转链接（订单详情的页面url）
        isPaySuccessfully: false,
        mpTracking: JSON.stringify({
          previousPageName: 'BookingDetail',
          previousModuleName: 'GotoPay'
        })
      })
      return
    }
    // whitelabel 判断
    const utilConfig = this.klook.utilConfig
    if (utilConfig.whiteLabelUtilConfig) {
      utilConfig.whiteLabelUtilConfig.sdk.payment({
        orderNo: order.order_guid,
        settlementType: 1,
        entrance: 1
      })
      return
    }
    if (deeplink) {
      window.location.href = deeplink
    }
  }

  getLangPath(path: string) {
    return getLangPathStr(this.klook.language, path)
  }

  get safeInfo() {
    const obj: any = this.calcActionList.find((item: ExperienceBookingDetails.ActionIF) => item.key === 'to_verify') || {}
    return obj
  }

  clickBtnStatus(btn: ExperienceBookingDetails.ActionIF) {
    switch (btn.key) {
      case 'contact_merchant':
        myEmit('showContactUs2on', { hideKlook: true }) // 隐藏klook
        break
      case 'refund': // 申请退款
        if (btn.tips && !this.isDesktop) {
          this.$toast({ message: btn.tips, duration: 4000 })
          return
        }
        this.manageRefundObj.activity_name = this.data.activity_name
        this.manageRefundObj.syncData.visible = !!btn.deep_link
        break
      case 'cancel_booking': // 取消预订
        if (btn.tips && !this.isDesktop) {
          this.$toast({ message: btn.tips, duration: 4000 })
          return
        }
        this.checkOrderCancel(this.data)
        break
      case 'continue_pay': // 继续付款
        this.checkOrderContinue(this.data, btn)
        break
      case 'refund_detail': // 退款详情
        this.showRefundDetail()
        break
      case 'app_voucher': // APP凭证 PC端弹出下载APP
        this.showDownload('booking_details')
        break
      case 'remind_ticket': // 催单
        this.clickRemindTicket()
        break
      case 'to_verify': // 去验证
        this.fraudLinkGuestCheckout(this.data, this.safeInfo?.deep_link)
        break
      case 'view_logistics': // 查看物流
        this.getLogistics()
        break
      case 'issue_franked_receipt': // 开具印花税收据时，关闭支付明细弹窗
        this.paymentDetailObj.on.close()
        break
      case 'view_voucher': {
        // wl通过sdk判断打开方式，有些app内部的view不支持直接查看pdf
        const sdk = this.klook.utilConfig?.whiteLabelUtilConfig?.sdk
        if (sdk) {
          sdk.openVoucher({ voucherUrl: btn.deep_link })
        } else if (btn.deep_link) {
          if (/\.pdf$/.test(btn.deep_link) && this.klook.isKlookApp && !this.klook.isIOS && this.user.isLimitedAccess) {
            openPdfWithFallback(btn.deep_link)
          } else {
            this.openDeeplink(btn.deep_link)
          }
        } else {
          this.$businessLog({
            tag: 'BookingDetail_Status_clickBtn',
            type: 'FECheckFail',
            reason: '校验不通过(wl 没有获取到 sdk 配置或链接为空)',
            btnKey: btn.key
          })
        }
        break
      }
      case 'issue_voucher_now': // delay voucher - 立即激活
        this.issueVoucherModal = true
        break
      case 'speed_it_up': // delay voucher - 催促出单
        this.speedItUpModal = true
        break
      default:
        if (btn.deep_link) {
          // desktop：重新预订（过期）、查看我的评价/去评价、无逻辑：已催单
          // mobile：查看凭证（pdf或img）、查看支付凭证、重新预订（过期）、查看我的评价/去评价、
          if (/\.pdf$/.test(btn.deep_link) && this.klook.isKlookApp && !this.klook.isIOS && this.user.isLimitedAccess) {
            openPdfWithFallback(btn.deep_link)
          } else {
            this.openDeeplink(btn.deep_link)
          }
        } else {
          if (btn.tips && !this.isDesktop) {
            // 新增兜底tips处理
            this.$toast({ message: btn.tips, duration: 4000 })
            return
          }
          this.$businessLog({
            tag: 'BookingDetail_Status_clickBtn',
            type: 'FECheckFail',
            reason: '校验不通过(没有获取到按钮链接)',
            btnKey: btn.key
          })
        }
    }
  }

  getLogistics() {
    const booking_reference_no = this.calcBookingRefNo
    this.$axios
      .get(apis.getLogistics, {
        loading: true,
        params: { booking_reference_no }
      })
      .then(({ data: resp }) => {
        if (!resp.success) {
          this.$alert(resp.error && resp.error.message)
          return
        }
        this.logisticsDetail = resp.result || null
        this.toggleLogistics()
      })
  }

  toggleLogistics() {
    if (this.isDesktop) {
      this.logisticsObj.open = true
    } else {
      this.showLogistics = !this.showLogistics
    }
  }

  fraudLinkGuestCheckout(orderDetail: any, link: string) {
    if (orderDetail.has_multiple_booking) {
      this.handleMergeOption(orderDetail.order_guid, 'verify')
    } else {
      this.handleFraudlink(link)
    }
  }

  hideMergeOption() {
    this.mergeOptionVisible = false
    this.mergeOptionInfo = null
  }

  handleMergeClick(type: string, order: any) {
    const { safeInfo } = this
    if (type === 'pay') {
      this.handleCheckContinue(order, this.mergeOptionInfo?.button?.deep_link)
    } else if (type === 'verify') {
      const link = safeInfo?.deep_link ||
        ''
      this.handleFraudlink(link)
    } else if (type === 'cancel') {
      this.cancelOrder(order.order_guid)
    }
    this.hideMergeOption()
  }

  handleFraudlink(link: string) {
    link && (window.location.href = link)
  }

  async clickPaymentDetails() {
    if (this.paymentDetailObj.cacheData) {
      this.paymentDetailObj.on.show()
      return
    }
    const booking_reference_no = this.calcBookingRefNo
    if (!booking_reference_no) {
      return
    }
    let res: any = null
    try {
      this.paymentDetailsLoading = true
      res = await this.$axios.$get(apis.getBookingPaymentDetailV3, {
        params: {
          booking_reference_no
        },
        checkLogin: true
      })
    } catch (error) {
      //
    }
    this.paymentDetailsLoading = false
    const {
      success,
      result,
      error = { message: 'Error' }
    } = res || {}
    if (success && result) {
      const { paymentDetailObj } = this
      // 处理 payment_details_buttons 数据格式转换
      const processedResult = {
        ...result,
        payment_details: result.payment_details ? {
          ...result.payment_details,
          payment_details_buttons: result.payment_details.payment_details_buttons?.map((button: any) => {
            const { button_link, button_text, ...rest } = button
            return {
              ...rest,
              button_text,
              deep_link: button_link,
              text: button_text
            }
          })
        } : undefined
      }
      const obj = { visible: true, booking_ref_no: this.calcBookingRefNo, ...processedResult }
      paymentDetailObj.data = obj
      paymentDetailObj.cacheData = processedResult
    } else {
      // 解决点击按钮获取数据失败时无响应问题
      this.$toast(error.message)
    }
  }

  openDeeplink(href: string) {
    openDeeplink(href, this.klook.platform)
  }

  updatePaymentButtonStatus(buttonUpdate: { oldKey: string; newKey: string; newText: string }) {
    const { paymentDetailObj } = this
    // 同时更新 cacheData 和 data 中的按钮状态，确保界面能够响应式更新
    if (paymentDetailObj.data?.payment_details?.payment_details_buttons) {
      paymentDetailObj.data.payment_details.payment_details_buttons.forEach((btn: any) => {
        if (btn.key === buttonUpdate.oldKey) {
          btn.key = buttonUpdate.newKey
          btn.text = buttonUpdate.newText
        }
      })
    }
  }

  onCustomButtonClick(_text: string, index: number) {
    const { data } = this.paymentDetailObj
    const btn = data?.payment_details?.payment_details_buttons?.[index]
    btn && this.clickPaymentDetailBtnStatus(btn)
  }

  clickPaymentDetailBtnStatus(btn: ExperienceBookingDetails.ActionIF) {
    switch (btn.key) {
      // 印花税收据
      case 'issue_franked_receipt': // 开具收据
        // web端需要弹出开具表单，mweb 直接跳转链接
        if (!this.isDesktop) {
          btn.deep_link && this.openDeeplink(btn.deep_link)
        } else {
          this.paymentDetailObj.on.close()
          myEmit('issu_franked_receipt', btn)
        }
        break
      // case 'view_franked_receipt_success': // 开具成功
      case 'issuing_franked_receipt': // 开办中 text id 13496
      case 'view_franked_receipt_fail': // 开具失败 text id 13782
        this.paymentDetailObj.on.close()
        myEmit('issu_franked_receipt', btn)
        break
      default:
        // deep_link
        btn.deep_link && this.openDeeplink(btn.deep_link)
    }
  }

  showAlert(type: number) {
    return type === 1
  }

  async handleIssueConfirm() {
    this.issueLoading = true
    const { success, error } = await this.$axios.$post(API_DELAY_VOUCHER_ISSUE_NOW, {
      order_no: this.calcOrderGuid,
      booking_no: this.calcBookingRefNo
    })
    this.issueLoading = false

    if (success) {
      // 激活成功刷新页面
      window.location.reload()
    } else {
      this.$alert(error?.message ?? 'Error')
    }
  }
}
</script>

<style lang="scss" scoped>
@import 'components/experience-booking/booking-details-v2/components/common/booking-status/base.scss';
.booking-status-box.dektop {
  .download-modal /deep/ .klk-modal {
    width: auto;
  }
}

.status-alert {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;

  &-icon {
    margin-right: 8px;
    flex-shrink: 0;
  }

  &-text {
    @include font-body-m-regular;
    color: $color-text-primary;
    white-space: pre-line;
  }
}

.issue-modal {
  .item {
    display: flex;

    .icon {
      margin-top: 4px;
    }

    .text {
      margin-left: 8px;
      @include font-body-m-regular;
    }
  }

  .footer {
    margin-top: 20px;
    display: flex;

    &.desktop {
      .klk-button {
        flex: 1;

        &:first-child {
          margin-right: 12px;
        }
      }
    }

    &.mobile {
      flex-direction: column;

      .klk-button {
        &:first-child {
          margin-bottom: 8px;
        }
      }
    }
  }
}

.speed-modal {
  text-align: center;

  .img {
    margin-top: 40px;
    width: 48px;
    height: 48px;
  }

  .title {
    margin-top: 20px;
    @include font-heading-xs;
  }

  .text {
    margin-top: 12px;
    text-align: left;
    @include font-body-m-regular;
  }
}
</style>
